const app = getApp();

Page({
  data: {
    modules: [
      { id: 1, type: 'basicInfo', name: '基本信息'},
      { id: 2, type: 'jobIntention', name: '求职意向'},
      { id: 3, type: 'education', name: '教育经历'},
      { id: 4, type: 'school', name: '在校经历'},
      { id: 5, type: 'internship', name: '实习经历'},
      { id: 6, type: 'work', name: '工作经历'},
      { id: 7, type: 'project', name: '项目经历'},
      { id: 8, type: 'skills', name: '技能特长'},
      { id: 9, type: 'awards', name: '奖项证书'},
      { id: 10, type: 'interests', name: '兴趣爱好'},
      { id: 11, type: 'evaluation', name: '自我评价'},
      { id: 12, type: 'custom1', name: '定制模块'},
      { id: 13, type: 'custom2', name: '定制模块'},
      { id: 14, type: 'custom3', name: '定制模块'}
    ],
    activeModules: [], // 存储已填写数据的模块 (包含所有有数据的模块)
    moduleOrders: {},
    itemHeight: 45,    // 每个模块的高度(px)

    // 拖拽相关
    currentIndex: -1,     // 当前拖拽的模块索引
    startY: 0,            // 拖拽开始时的Y坐标
    moveY: 0,             // 当前移动的Y坐标
    dragging: false,      // 是否正在拖拽
    dragOffset: 0,        // 拖拽偏移量
    dropIndex: -1,        // 最终释放的索引位置
    lastMoveTime: 0,      // 上次移动时间，用于防抖
    itemOffsets: []       // 每个项目的实时偏移量数组
  },

  onLoad: function(options) {
    console.log('========== 模块管理页面 onLoad ==========');
    // 从全局简历管理器加载数据
    this.loadActiveModules();
  },

  onReady: function() {
    // 页面渲染完成后处理
    console.log('页面渲染完成');

    // 获取元素高度
    this.getItemHeight();
  },

  // 获取模块元素的实际高度
  getItemHeight: function() {
    const query = wx.createSelectorQuery().in(this);
    query.select('.module-item').boundingClientRect();
    query.exec(res => {
      if (res && res[0]) {
        this.setData({
          itemHeight: res[0].height || 45
        });
        console.log('模块实际高度:', res[0].height, 'px');
      }
    });
  },

  // 加载已填写数据的模块
  loadActiveModules: function() {
    console.log('========== 开始加载模块数据 ==========');

    try {
      // 通过全局简历管理器获取当前简历数据
      const resumeManager = app.getResumeManager();
      const currentResume = resumeManager.getCurrentResume();

      if (!currentResume) {
        console.error('无法获取当前简历数据');
        return;
      }

      console.log('从全局简历管理器获取的数据:', currentResume);

      // 获取模块顺序
      let moduleOrders = currentResume.moduleOrders || {};
      console.log('当前模块顺序:', moduleOrders);

      // 检查各模块是否有数据，创建活动模块列表
      const filledModules = [];

      // 检查基本信息
      if (currentResume.basicInfo && currentResume.basicInfo.name) {
        filledModules.push('basicInfo');
      }

      // 检查求职意向
      const jobIntention = currentResume.jobIntention;
      if (jobIntention && (jobIntention.position || jobIntention.location || jobIntention.salary || jobIntention.status)) {
        filledModules.push('jobIntention');
      }

      // 检查教育经历
      if (currentResume.education && currentResume.education.length > 0) {
        filledModules.push('education');
      }

      // 检查在校经历
      if (currentResume.school && currentResume.school.length > 0) {
        filledModules.push('school');
      }

      // 检查实习经历
      if (currentResume.internship && currentResume.internship.length > 0) {
        filledModules.push('internship');
      }

      // 检查工作经历
      if (currentResume.work && currentResume.work.length > 0) {
        filledModules.push('work');
      }

      // 检查项目经历
      if (currentResume.project && currentResume.project.length > 0) {
        filledModules.push('project');
      }

      // 检查技能特长
      if (currentResume.skills && currentResume.skills.toArray().length > 0) {
        filledModules.push('skills');
      }

      // 检查奖项证书
      if (currentResume.awards && currentResume.awards.toArray().length > 0) {
        filledModules.push('awards');
      }

      // 检查兴趣爱好
      if (currentResume.interests && currentResume.interests.toArray().length > 0) {
        filledModules.push('interests');
      }

      // 检查自我评价
      if (currentResume.evaluation && currentResume.evaluation.length > 0) {
        filledModules.push('evaluation');
      }

      // 检查自定义模块
      if (currentResume.custom1 && currentResume.custom1.length > 0 &&
          currentResume.custom1.some(item => item.customName || item.content || item.role)) {
        filledModules.push('custom1');
      }
      if (currentResume.custom2 && currentResume.custom2.length > 0 &&
          currentResume.custom2.some(item => item.customName || item.content || item.role)) {
        filledModules.push('custom2');
      }
      if (currentResume.custom3 && currentResume.custom3.length > 0 &&
          currentResume.custom3.some(item => item.customName || item.content || item.role)) {
        filledModules.push('custom3');
      }

      console.log('检测到有数据的模块:', filledModules);

      // 补全模块顺序（确保所有模块都有顺序值）
      moduleOrders = this.completeFillModuleOrders(moduleOrders);

      // 过滤出有数据的模块
      let activeModules = this.data.modules.filter(module =>
        filledModules.includes(module.type)
      );

      // 按照模块顺序排序
      activeModules = activeModules.sort((a, b) => {
        const orderA = moduleOrders[a.type] !== undefined ? moduleOrders[a.type] : Infinity;
        const orderB = moduleOrders[b.type] !== undefined ? moduleOrders[b.type] : Infinity;
        return orderA - orderB;
      });

      console.log('排序后的活动模块:', activeModules.map(m => `${m.name}(${moduleOrders[m.type]})`));

      // 更新数据
      this.setData({
        activeModules: activeModules,
        moduleOrders: moduleOrders
      });

      console.log('========== 模块数据加载完成 ==========');
    } catch (error) {
      console.error('加载模块数据失败:', error);
    }
  },

  // 补全模块顺序
  completeFillModuleOrders(moduleOrders) {
    console.log('========== 补全模块顺序 ==========');
    console.log('补全前的moduleOrders:', moduleOrders);

    // 找到当前最大的顺序值
    let maxOrder = 0;
    this.data.modules.forEach(module => {
      if (moduleOrders[module.type] !== undefined) {
        maxOrder = Math.max(maxOrder, moduleOrders[module.type]);
      }
    });

    console.log('当前最大顺序值:', maxOrder);

    // 为没有顺序的模块分配顺序值
    this.data.modules.forEach(module => {
      const type = module.type;
      if (moduleOrders[type] === undefined) {
        maxOrder++;
        moduleOrders[type] = maxOrder;
      }
    });

    console.log('补全后的moduleOrders:', moduleOrders);
    return moduleOrders;
  },

  // 触摸开始
  handleTouchStart: function(e) {
    // 获取当前拖拽的模块索引
    const index = e.currentTarget.dataset.index;

    console.log('触摸开始', index);

    // 初始化所有项目的偏移量数组
    const itemOffsets = new Array(this.data.activeModules.length).fill(0);

    this.setData({
      currentIndex: index,
      startY: e.touches[0].clientY,
      moveY: e.touches[0].clientY,
      dragging: true,
      dragOffset: 0,
      dropIndex: index,
      lastMoveTime: Date.now(),
      itemOffsets: itemOffsets
    });
  },

  // 触摸移动
  handleTouchMove: function(e) {
    const { dragging, currentIndex, lastMoveTime, startY, itemOffsets } = this.data;

    if (!dragging) return;

    // 简单的防抖处理
    const now = Date.now();
    if (now - lastMoveTime < 32) { // 约30fps
      return;
    }

    const moveY = e.touches[0].clientY;
    const dragOffset = moveY - startY;
    const activeModulesCount = this.data.activeModules.length;

    // 计算目标位置索引
    const itemHeight = this.data.itemHeight;
    let offsetIndex = Math.round(dragOffset / itemHeight);
    let targetIndex = currentIndex + offsetIndex;

    // 限制目标索引在有效范围内（所有模块都可以参与排序）
    targetIndex = Math.max(0, Math.min(targetIndex, activeModulesCount - 1));

    // 计算所有项目的实时偏移量（使用rpx单位）
    const newItemOffsets = this.calculateItemOffsets(currentIndex, targetIndex, 90); // 90rpx对应CSS中的height

    // 更新数据
    this.setData({
      moveY: moveY,
      dragOffset: dragOffset,
      dropIndex: targetIndex,
      lastMoveTime: now,
      itemOffsets: newItemOffsets
    });
  },

  // 计算所有项目的实时偏移量
  calculateItemOffsets: function(currentIndex, targetIndex, itemHeightRpx) {
    const activeModulesCount = this.data.activeModules.length;
    const offsets = new Array(activeModulesCount).fill(0);

    // 如果目标位置和当前位置相同，不需要偏移
    if (currentIndex === targetIndex) {
      return offsets;
    }

    // 计算其他项目的偏移量（使用rpx单位）
    if (targetIndex > currentIndex) {
      // 向下拖拽：currentIndex+1 到 targetIndex 的项目向上移动
      for (let i = currentIndex + 1; i <= targetIndex; i++) {
        offsets[i] = -itemHeightRpx;
      }
    } else {
      // 向上拖拽：targetIndex 到 currentIndex-1 的项目向下移动
      for (let i = targetIndex; i < currentIndex; i++) {
        offsets[i] = itemHeightRpx;
      }
    }

    return offsets;
  },

  // 触摸结束
  handleTouchEnd: function() {
    const { dragging, currentIndex, dropIndex } = this.data;

    if (!dragging) return;

    console.log('拖拽结束', { currentIndex, dropIndex });

    if (currentIndex !== dropIndex && dropIndex !== -1) {
      // 先让被拖拽项目平滑移动到目标位置
      this.setData({
        dragging: false,
        dragOffset: 0  // 重置拖拽偏移，让被拖拽项目回到原位
      });

      // 等待动画完成后执行实际排序
      setTimeout(() => {
        this.moveItem(currentIndex, dropIndex);

        // 重置所有状态
        this.setData({
          currentIndex: -1,
          dropIndex: -1,
          itemOffsets: []
        });
      }, 250); // 与CSS transition时间一致
    } else {
      // 如果没有移动或移动无效，则直接平滑动画回原位
      this.setData({
        dragging: false,
        currentIndex: -1,
        dragOffset: 0,
        dropIndex: -1,
        itemOffsets: []
      });
    }
  },

  // 移动项目到目标位置
  moveItem: function(fromIndex, toIndex) {
    console.log(`移动模块：从 ${fromIndex} 到 ${toIndex}`);

    if (fromIndex === toIndex) return;

    const activeModules = this.data.activeModules;

    const currentItem = activeModules.splice(fromIndex, 1)[0];

    activeModules.splice(toIndex, 0, currentItem);

    this.setData({
      activeModules: activeModules
    });
  },

  // 保存模块设置
  saveModuleSettings: function() {
    console.log('========== 开始保存模块设置 ==========');

    try {
      const activeModules = [...this.data.activeModules]; // 创建副本避免修改原数据
      console.log('当前排序后的模块:', activeModules.map(m => m.name));

      // 创建新的模块顺序对象
      const newModuleOrders = {};

      // 检查是否有 basicInfo 模块
      const basicInfoIndex = activeModules.findIndex(module => module.type === 'basicInfo');

      if (basicInfoIndex !== -1) {
        // 如果有 basicInfo，先移除它
        const basicInfoModule = activeModules.splice(basicInfoIndex, 1)[0];
        console.log('找到 basicInfo 模块，将其移到第一位');

        // basicInfo 始终排在第一位（order = 0）
        newModuleOrders['basicInfo'] = 0;

        // 其他模块从 1 开始排序
        activeModules.forEach((module, index) => {
          newModuleOrders[module.type] = index + 1;
        });

        // 重新构建完整的 activeModules 列表（用于显示）
        activeModules.unshift(basicInfoModule);
      } else {
        // 如果没有 basicInfo，直接从 0 开始排序
        activeModules.forEach((module, index) => {
          newModuleOrders[module.type] = index;
        });
      }

      console.log('新的模块顺序:', newModuleOrders);

      // 通过全局简历管理器保存数据
      const resumeManager = app.getResumeManager();
      const currentResume = resumeManager.getCurrentResume();

      if (currentResume) {
        // 更新简历实例中的模块顺序
        currentResume.moduleOrders = newModuleOrders;

        // 保存到存储
        resumeManager.saveToStorage();

        console.log('模块顺序已保存到全局简历管理器');
      } else {
        console.error('无法获取当前简历实例');
        wx.showToast({ title: '保存失败', icon: 'none' });
        return;
      }

      // 兼容性：同时保存到旧的存储方式
      wx.setStorageSync('activeModules', activeModules);
      wx.setStorageSync('moduleOrders', newModuleOrders);

      console.log('========== 模块设置保存完成 ==========');

      // 显示成功提示并返回
      wx.showToast({ title: '保存成功', icon: 'success', duration: 500 });
      setTimeout(() => { wx.navigateBack(); }, 500);

    } catch (error) {
      console.error('保存模块设置失败:', error);
      wx.showToast({ title: '保存失败', icon: 'none' });
    }
  }
});