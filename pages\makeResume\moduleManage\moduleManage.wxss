.module-manage {
  padding: 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
  box-sizing: border-box;
}

.module-list {
  background: #fff;
  border-radius: 12rpx;
  padding: 0;
  margin-bottom: 40rpx;
  overflow: hidden;
  position: relative;
  min-height: 600rpx;
}

.module-container {
  width: 100%;
  padding: 0;
  position: relative;
}

.module-item {
  padding: 0 20rpx;
  background-color: #fff;
  z-index: 1;
  box-sizing: border-box;
  margin-bottom: 1rpx;
  border-bottom: 1rpx solid #eee;
  width: 100%;
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94), background-color 0.2s ease;
  transform: translateZ(0);
  will-change: transform;
}

.module-content {
  display: flex;
  align-items: center;
  height: 90rpx;
  width: 100%;
}

.dragging {
  background-color: #f0f8ff;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
  z-index: 20;
  border-bottom: 1rpx dashed #4080ff;
  opacity: 0.9;
  transition: none !important;
}

.drop-target {
  border-top: 1rpx solid #4080ff;
  background-color: rgba(64, 128, 255, 0.05);
}

.drag-icon {
  color: #505050;
  /* background-color: #69b91e; */
  font-size: 40rpx;
  padding: 0 20rpx 0 0;
  display: flex;
  align-items: center;
}

.module-name {
  font-size: 32rpx;
  color: #000000;
  /* background-color: #1e47b9; */
  flex: 1;
}

.basic-info-item {
  background-color: #f8f9ff;
  border-left: 4rpx solid #4080ff;
}

.basic-info-item .module-name {
  color: #4080ff;
  font-weight: 500;
}

.module-tip {
  font-size: 24rpx;
  color: #999;
  margin-left: 10rpx;
}

.tip-text {
  text-align: center;
  font-size: 28rpx;
  color: #999;
  padding: 40rpx 0;
}

.save-btn {
  position: fixed;
  bottom: 40rpx;
  left: 40rpx;
  right: 40rpx;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  background: #4080ff;
  color: #fff;
  border-radius: 44rpx;
  font-size: 32rpx;
  box-shadow: 0 4rpx 8rpx rgba(64,128,255,0.2);
}