<!-- 模块管理页面 -->
<view class="module-manage" catchmove="{{dragging ? 'catchTouchMove' : ''}}">
  <view class="module-list">
    <!-- 所有模块列表（包括 basicInfo，但 basicInfo 有特殊样式提示） -->
    <view class="module-container">
      <view
        wx:for="{{activeModules}}"
        wx:key="id"
        class="module-item {{item.type === 'basicInfo' ? 'basic-info-item' : ''}} {{currentIndex === index ? 'dragging' : ''}} {{dropIndex === index && currentIndex !== index ? 'drop-target' : ''}}"
        style="{{currentIndex === index ? 'transform: translateY(' + dragOffset + 'px);' : (itemOffsets[index] ? 'transform: translateY(' + itemOffsets[index] + 'rpx);' : '')}}"
        data-index="{{index}}"
        bindtouchstart="handleTouchStart"
        bindtouchmove="handleTouchMove"
        bindtouchend="handleTouchEnd"
        bindtouchcancel="handleTouchEnd">
        <view class="module-content">
          <view class="drag-icon">{{item.type === 'basicInfo' ? '📌' : '≡'}}</view>
          <view class="module-name">{{item.name}}</view>
          <view class="module-tip" wx:if="{{item.type === 'basicInfo'}}">（置顶）</view>
        </view>
      </view>
    </view>
  </view>

  <view class="tip-text" wx:if="{{!activeModules.length}}">暂无已填写的模块内容</view>
  <view class="save-btn" bindtap="saveModuleSettings" wx:if="{{activeModules.length}}">保存设置</view>
</view>